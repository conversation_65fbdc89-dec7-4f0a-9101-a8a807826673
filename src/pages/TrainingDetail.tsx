import { useParams, Navigate } from 'react-router-dom';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import Layout from '@/components/Layout';
import { ContentProvider } from '@/components/content/ContentProvider';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, CheckCircle, ArrowLeft, Clock, Users, GraduationCap, Target, Monitor, Award } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Skeleton } from '@/components/ui/skeleton';
import { useLanguage, useTranslation } from '@/contexts/I18nContext';
import { BreadcrumbNav, generateBreadcrumbs } from '@/components/BreadcrumbNav';

const TrainingDetailContent = () => {
  const { slug } = useParams<{ slug: string }>();
  const { language } = useLanguage();
  const { t } = useTranslation();

  // Get all training programs to find the one with matching slug
  const allContent = useQuery(api.content.getAllContent, {
    language: language,
    status: "published"
  });

  // Filter for training programs and find the one with matching slug
  const trainingProgram = allContent?.find(item =>
    item.contentType?.name === "training_program" && item.data.slug === slug
  );

  // Get the other language version for fallback
  const otherLanguage = language === 'en' ? 'es' : 'en';
  const fallbackContent = useQuery(api.content.getContent, {
    identifier: trainingProgram?.identifier || "",
    language: otherLanguage,
    includeDraft: false
  });

  // Loading state
  if (allContent === undefined) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <Skeleton className="h-8 w-64 mb-4" />
          <Skeleton className="h-64 w-full mb-8" />
          <Skeleton className="h-32 w-full" />
        </div>
      </div>
    );
  }

  // Fallback training programs data for static content - matching TrainingDropdown
  const fallbackPrograms = [
    {
      id: 'corporate-pc-skills',
      title: language === 'en' ? 'Corporate PC Skills Development' : 'Desarrollo de Habilidades de PC Corporativas',
      duration: language === 'en' ? '2-4 weeks' : '2-4 semanas',
      level: language === 'en' ? 'All Levels' : 'Todos los Niveles',
      participants: '15-20',
      description: language === 'en' ? 'Essential computer skills for the modern workplace' : 'Habilidades informáticas esenciales para el lugar de trabajo moderno',
      fullDescription: language === 'en' ?
        'Comprehensive training programs designed to empower your team with the knowledge and expertise they need to excel in the workplace.' :
        'Programas de entrenamiento integrales diseñados para empoderar a tu equipo con el conocimiento y la experiencia que necesitan para sobresalir en el lugar de trabajo.',
      topics: language === 'en' ? [
        'Microsoft Office Suite (Word, Excel, PowerPoint, Outlook, Teams)',
        'Windows Operating Systems navigation and troubleshooting',
        'Cybersecurity Awareness and best practices',
        'Network Fundamentals and basic troubleshooting',
        'Custom Training tailored to business needs'
      ] : [
        'Suite de Microsoft Office (Word, Excel, PowerPoint, Outlook, Teams)',
        'Navegación de Sistemas Operativos Windows y solución de problemas',
        'Conciencia de Ciberseguridad y mejores prácticas',
        'Fundamentos de Redes y solución básica de problemas',
        'Entrenamiento Personalizado adaptado a necesidades empresariales'
      ],
      benefits: language === 'en' ? [
        'Improved Productivity',
        'Enhanced Skills',
        'Better Security Awareness',
        'Competitive Advantage'
      ] : [
        'Productividad Mejorada',
        'Habilidades Mejoradas',
        'Mejor Conciencia de Seguridad',
        'Ventaja Competitiva'
      ],
      deliveryOptions: language === 'en' ? [
        'On-site Training',
        'Virtual Training',
        'Hybrid Learning',
        'Self-paced Modules'
      ] : [
        'Entrenamiento en Sitio',
        'Entrenamiento Virtual',
        'Aprendizaje Híbrido',
        'Módulos de Ritmo Propio'
      ],
      targetAudience: language === 'en' ? [
        'Business professionals',
        'Office workers',
        'IT support staff',
        'Administrative personnel'
      ] : [
        'Profesionales de negocios',
        'Trabajadores de oficina',
        'Personal de soporte de TI',
        'Personal administrativo'
      ],
      icon: Monitor,
      price: language === 'en' ? 'Contact for pricing' : 'Contactar para precios',
      slug: 'corporate-pc-skills'
    },
    {
      id: 'leadership-training',
      title: language === 'en' ? 'Leadership Development' : 'Desarrollo de Liderazgo',
      duration: language === 'en' ? '3-6 weeks' : '3-6 semanas',
      level: language === 'en' ? 'Intermediate to Advanced' : 'Intermedio a Avanzado',
      participants: '10-15',
      description: language === 'en' ? 'Essential skills to lead effectively and inspire others' : 'Habilidades esenciales para liderar efectivamente e inspirar a otros',
      fullDescription: language === 'en' ?
        'Develop the essential skills needed to lead teams effectively, inspire others, and drive organizational success.' :
        'Desarrolla las habilidades esenciales necesarias para liderar equipos efectivamente, inspirar a otros y impulsar el éxito organizacional.',
      topics: language === 'en' ? [
        'Strategic thinking and vision development',
        'Team building and management',
        'Effective communication and presentation',
        'Decision making frameworks',
        'Change management and adaptation'
      ] : [
        'Pensamiento estratégico y desarrollo de visión',
        'Construcción y gestión de equipos',
        'Comunicación efectiva y presentación',
        'Marcos de toma de decisiones',
        'Gestión del cambio y adaptación'
      ],
      benefits: language === 'en' ? [
        'Enhanced Leadership Skills',
        'Better Team Performance',
        'Improved Decision Making',
        'Career Advancement'
      ] : [
        'Habilidades de Liderazgo Mejoradas',
        'Mejor Rendimiento del Equipo',
        'Toma de Decisiones Mejorada',
        'Avance Profesional'
      ],
      deliveryOptions: language === 'en' ? [
        'Executive Coaching',
        'Group Workshops',
        'Leadership Retreats',
        'Mentoring Programs'
      ] : [
        'Coaching Ejecutivo',
        'Talleres Grupales',
        'Retiros de Liderazgo',
        'Programas de Mentoría'
      ],
      targetAudience: language === 'en' ? [
        'Senior managers',
        'Team leaders',
        'Department heads',
        'Aspiring leaders'
      ] : [
        'Gerentes senior',
        'Líderes de equipo',
        'Jefes de departamento',
        'Líderes aspirantes'
      ],
      icon: Target,
      price: language === 'en' ? 'Contact for pricing' : 'Contactar para precios',
      slug: 'leadership-training'
    }
