import { useParams, Navigate } from 'react-router-dom';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import Layout from '@/components/Layout';
import { ContentProvider } from '@/components/content/ContentProvider';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, CheckCircle, ArrowLeft, Clock, Users, GraduationCap, Target, Monitor, Award } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Skeleton } from '@/components/ui/skeleton';
import { useLanguage, useTranslation } from '@/contexts/I18nContext';
import { BreadcrumbNav, generateBreadcrumbs } from '@/components/BreadcrumbNav';

const TrainingDetailContent = () => {
  const { slug } = useParams<{ slug: string }>();
  const { language } = useLanguage();
  const { t } = useTranslation();

  // Get all training programs to find the one with matching slug
  const allContent = useQuery(api.content.getAllContent, {
    language: language,
    status: "published"
  });

  // Filter for training programs and find the one with matching slug
  const trainingProgram = allContent?.find(item =>
    item.contentType?.name === "training_program" && item.data.slug === slug
  );

  // Get the other language version for fallback
  const otherLanguage = language === 'en' ? 'es' : 'en';
  const fallbackContent = useQuery(api.content.getContent, {
    identifier: trainingProgram?.identifier || "",
    language: otherLanguage,
    includeDraft: false
  });

  // Loading state
  if (allContent === undefined) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <Skeleton className="h-8 w-64 mb-4" />
          <Skeleton className="h-64 w-full mb-8" />
          <Skeleton className="h-32 w-full" />
        </div>
      </div>
    );
  }

  // If no training program found, redirect to training page
  if (!trainingProgram) {
    return <Navigate to="/training" replace />;
  }

  // Fallback training programs data for static content - matching TrainingDropdown
  const fallbackPrograms = [
    {
      id: 'corporate-pc-skills',
      title: language === 'en' ? 'Corporate PC Skills Development' : 'Desarrollo de Habilidades de PC Corporativas',
      duration: language === 'en' ? '2-4 weeks' : '2-4 semanas',
      level: language === 'en' ? 'All Levels' : 'Todos los Niveles',
      participants: '15-20',
      description: language === 'en' ? 'Essential computer skills for the modern workplace' : 'Habilidades informáticas esenciales para el lugar de trabajo moderno',
      fullDescription: language === 'en' ?
        `<h2>Corporate PC Skills Development</h2>
        <p>At OfficeTech, we understand that in today's fast-paced business environment, having strong computer skills is essential for success. That's why we offer comprehensive training programs designed to empower your team with the knowledge and expertise they need to excel in the workplace.</p>

        <h3>Training Programs Include:</h3>
        <ul>
          <li><strong>Microsoft Office Suite:</strong> Word, Excel, PowerPoint, Outlook, and Teams</li>
          <li><strong>Windows Operating Systems:</strong> Navigation, file management, and troubleshooting</li>
          <li><strong>Cybersecurity Awareness:</strong> Best practices for online safety and data protection</li>
          <li><strong>Network Fundamentals:</strong> Basic networking concepts and troubleshooting</li>
          <li><strong>Custom Training:</strong> Tailored programs for specific business needs</li>
        </ul>

        <p>Whether you're looking to enhance productivity, streamline operations, or simply stay ahead of the curve, our tailored training solutions are here to help.</p>` :
        `<h2>Desarrollo de Habilidades de PC Corporativas</h2>
        <p>En OfficeTech, entendemos que en el entorno empresarial acelerado de hoy, tener habilidades informáticas sólidas es esencial para el éxito. Por eso ofrecemos programas de entrenamiento integrales diseñados para empoderar a tu equipo con el conocimiento y la experiencia que necesitan para sobresalir en el lugar de trabajo.</p>

        <h3>Los Programas de Entrenamiento Incluyen:</h3>
        <ul>
          <li><strong>Suite de Microsoft Office:</strong> Word, Excel, PowerPoint, Outlook y Teams</li>
          <li><strong>Sistemas Operativos Windows:</strong> Navegación, gestión de archivos y solución de problemas</li>
          <li><strong>Conciencia de Ciberseguridad:</strong> Mejores prácticas para seguridad en línea y protección de datos</li>
          <li><strong>Fundamentos de Redes:</strong> Conceptos básicos de redes y solución de problemas</li>
          <li><strong>Entrenamiento Personalizado:</strong> Programas adaptados para necesidades empresariales específicas</li>
        </ul>

        <p>Ya sea que busques mejorar la productividad, optimizar operaciones o simplemente mantenerte a la vanguardia, nuestras soluciones de entrenamiento personalizadas están aquí para ayudar.</p>`,
      topics: language === 'en' ? [
        'Microsoft Office Suite (Word, Excel, PowerPoint, Outlook, Teams)',
        'Windows Operating Systems navigation and troubleshooting',
        'Cybersecurity Awareness and best practices',
        'Network Fundamentals and basic troubleshooting',
        'Custom Training tailored to business needs'
      ] : [
        'Suite de Microsoft Office (Word, Excel, PowerPoint, Outlook, Teams)',
        'Navegación de Sistemas Operativos Windows y solución de problemas',
        'Conciencia de Ciberseguridad y mejores prácticas',
        'Fundamentos de Redes y solución básica de problemas',
        'Entrenamiento Personalizado adaptado a necesidades empresariales'
      ],
      benefits: language === 'en' ? [
        'Improved Productivity',
        'Enhanced Skills',
        'Better Security Awareness',
        'Competitive Advantage'
      ] : [
        'Productividad Mejorada',
        'Habilidades Mejoradas',
        'Mejor Conciencia de Seguridad',
        'Ventaja Competitiva'
      ],
      deliveryOptions: language === 'en' ? [
        'On-site Training',
        'Virtual Training',
        'Hybrid Learning',
        'Self-paced Modules'
      ] : [
        'Entrenamiento en Sitio',
        'Entrenamiento Virtual',
        'Aprendizaje Híbrido',
        'Módulos de Ritmo Propio'
      ],
      targetAudience: language === 'en' ? [
        'Business professionals',
        'Office workers',
        'IT support staff',
        'Administrative personnel'
      ] : [
        'Profesionales de negocios',
        'Trabajadores de oficina',
        'Personal de soporte de TI',
        'Personal administrativo'
      ],
      icon: Monitor,
      price: language === 'en' ? 'Contact for pricing' : 'Contactar para precios',
      slug: 'corporate-pc-skills'
    },
    {
      id: 'leadership-training',
      title: language === 'en' ? 'Leadership Development' : 'Desarrollo de Liderazgo',
      duration: language === 'en' ? '3-6 weeks' : '3-6 semanas',
      level: language === 'en' ? 'Intermediate to Advanced' : 'Intermedio a Avanzado',
      participants: '10-15',
      description: language === 'en' ? 'Essential skills to lead effectively and inspire others' : 'Habilidades esenciales para liderar efectivamente e inspirar a otros',
      fullDescription: language === 'en' ?
        `<h2>Leadership Development Program</h2>
        <p>Develop the essential skills needed to lead teams effectively, inspire others, and drive organizational success. Our comprehensive leadership program covers both traditional and modern leadership approaches.</p>

        <h3>Key Areas of Focus:</h3>
        <ul>
          <li><strong>Strategic Thinking:</strong> Develop long-term vision and planning skills</li>
          <li><strong>Team Management:</strong> Build and lead high-performing teams</li>
          <li><strong>Communication:</strong> Master effective communication and presentation skills</li>
          <li><strong>Decision Making:</strong> Learn frameworks for effective decision making</li>
          <li><strong>Change Management:</strong> Lead organizational change initiatives</li>
        </ul>` :
        `<h2>Programa de Desarrollo de Liderazgo</h2>
        <p>Desarrolla las habilidades esenciales necesarias para liderar equipos efectivamente, inspirar a otros y impulsar el éxito organizacional. Nuestro programa integral de liderazgo cubre enfoques de liderazgo tanto tradicionales como modernos.</p>

        <h3>Áreas Clave de Enfoque:</h3>
        <ul>
          <li><strong>Pensamiento Estratégico:</strong> Desarrolla habilidades de visión y planificación a largo plazo</li>
          <li><strong>Gestión de Equipos:</strong> Construye y lidera equipos de alto rendimiento</li>
          <li><strong>Comunicación:</strong> Domina habilidades efectivas de comunicación y presentación</li>
          <li><strong>Toma de Decisiones:</strong> Aprende marcos para la toma efectiva de decisiones</li>
          <li><strong>Gestión del Cambio:</strong> Lidera iniciativas de cambio organizacional</li>
        </ul>`,
      topics: language === 'en' ? [
        'Strategic thinking and vision development',
        'Team building and management',
        'Effective communication and presentation',
        'Decision making frameworks',
        'Change management and adaptation'
      ] : [
        'Pensamiento estratégico y desarrollo de visión',
        'Construcción y gestión de equipos',
        'Comunicación efectiva y presentación',
        'Marcos de toma de decisiones',
        'Gestión del cambio y adaptación'
      ],
      benefits: language === 'en' ? [
        'Enhanced Leadership Skills',
        'Better Team Performance',
        'Improved Decision Making',
        'Career Advancement'
      ] : [
        'Habilidades de Liderazgo Mejoradas',
        'Mejor Rendimiento del Equipo',
        'Toma de Decisiones Mejorada',
        'Avance Profesional'
      ],
      deliveryOptions: language === 'en' ? [
        'Executive Coaching',
        'Group Workshops',
        'Leadership Retreats',
        'Mentoring Programs'
      ] : [
        'Coaching Ejecutivo',
        'Talleres Grupales',
        'Retiros de Liderazgo',
        'Programas de Mentoría'
      ],
      targetAudience: language === 'en' ? [
        'Senior managers',
        'Team leaders',
        'Department heads',
        'Aspiring leaders'
      ] : [
        'Gerentes senior',
        'Líderes de equipo',
        'Jefes de departamento',
        'Líderes aspirantes'
      ],
      icon: Target,
      price: language === 'en' ? 'Contact for pricing' : 'Contactar para precios',
      slug: 'leadership-training'
    },
    {
      id: 'time-management',
      title: language === 'en' ? 'Time Management' : 'Gestión del Tiempo',
      duration: language === 'en' ? '1-2 weeks' : '1-2 semanas',
      level: language === 'en' ? 'All Levels' : 'Todos los Niveles',
      participants: '20-25',
      description: language === 'en' ? 'Take control of schedules and achieve more with less stress' : 'Toma control de horarios y logra más con menos estrés',
      fullDescription: language === 'en' ?
        `<h2>Time Management Mastery</h2>
        <p>Learn proven strategies to maximize productivity, reduce stress, and achieve better work-life balance. This program provides practical tools and techniques for effective time management.</p>

        <h3>What You'll Learn:</h3>
        <ul>
          <li><strong>Priority Setting:</strong> Identify and focus on high-impact activities</li>
          <li><strong>Planning Techniques:</strong> Daily, weekly, and monthly planning strategies</li>
          <li><strong>Productivity Tools:</strong> Digital and analog tools for time tracking</li>
          <li><strong>Stress Management:</strong> Techniques to reduce overwhelm and burnout</li>
          <li><strong>Work-Life Balance:</strong> Strategies for maintaining healthy boundaries</li>
        </ul>` :
        `<h2>Dominio de la Gestión del Tiempo</h2>
        <p>Aprende estrategias probadas para maximizar la productividad, reducir el estrés y lograr un mejor equilibrio trabajo-vida. Este programa proporciona herramientas y técnicas prácticas para la gestión efectiva del tiempo.</p>

        <h3>Lo Que Aprenderás:</h3>
        <ul>
          <li><strong>Establecimiento de Prioridades:</strong> Identifica y enfócate en actividades de alto impacto</li>
          <li><strong>Técnicas de Planificación:</strong> Estrategias de planificación diaria, semanal y mensual</li>
          <li><strong>Herramientas de Productividad:</strong> Herramientas digitales y analógicas para seguimiento del tiempo</li>
          <li><strong>Gestión del Estrés:</strong> Técnicas para reducir la sobrecarga y el agotamiento</li>
          <li><strong>Equilibrio Trabajo-Vida:</strong> Estrategias para mantener límites saludables</li>
        </ul>`,
      topics: language === 'en' ? [
        'Priority setting and goal alignment',
        'Daily and weekly planning systems',
        'Productivity tools and techniques',
        'Stress management strategies',
        'Work-life balance principles'
      ] : [
        'Establecimiento de prioridades y alineación de objetivos',
        'Sistemas de planificación diaria y semanal',
        'Herramientas y técnicas de productividad',
        'Estrategias de gestión del estrés',
        'Principios de equilibrio trabajo-vida'
      ],
      benefits: language === 'en' ? [
        'Increased Productivity',
        'Reduced Stress',
        'Better Work-Life Balance',
        'Improved Focus'
      ] : [
        'Productividad Aumentada',
        'Estrés Reducido',
        'Mejor Equilibrio Trabajo-Vida',
        'Enfoque Mejorado'
      ],
      deliveryOptions: language === 'en' ? [
        'Interactive Workshops',
        'Online Modules',
        'Personal Coaching',
        'Group Sessions'
      ] : [
        'Talleres Interactivos',
        'Módulos en Línea',
        'Coaching Personal',
        'Sesiones Grupales'
      ],
      targetAudience: language === 'en' ? [
        'Busy professionals',
        'Project managers',
        'Entrepreneurs',
        'Anyone seeking better productivity'
      ] : [
        'Profesionales ocupados',
        'Gerentes de proyecto',
        'Emprendedores',
        'Cualquiera que busque mejor productividad'
      ],
      icon: Clock,
      price: language === 'en' ? 'Contact for pricing' : 'Contactar para precios',
      slug: 'time-management'
    },
    {
      id: 'communication-skills',
      title: language === 'en' ? 'Communication Skills' : 'Habilidades de Comunicación',
      duration: language === 'en' ? '2-3 weeks' : '2-3 semanas',
      level: language === 'en' ? 'All Levels' : 'Todos los Niveles',
      participants: '15-20',
      description: language === 'en' ? 'Master the art of effective communication' : 'Domina el arte de la comunicación efectiva',
      fullDescription: language === 'en' ?
        `<h2>Communication Skills Mastery</h2>
        <p>Develop exceptional communication skills that will enhance your professional relationships, improve team collaboration, and boost your career prospects.</p>

        <h3>Core Components:</h3>
        <ul>
          <li><strong>Verbal Communication:</strong> Clear, confident speaking and presentation skills</li>
          <li><strong>Written Communication:</strong> Professional writing and documentation</li>
          <li><strong>Active Listening:</strong> Techniques for better understanding and engagement</li>
          <li><strong>Non-verbal Communication:</strong> Body language and visual communication</li>
          <li><strong>Conflict Resolution:</strong> Managing difficult conversations effectively</li>
        </ul>` :
        `<h2>Dominio de Habilidades de Comunicación</h2>
        <p>Desarrolla habilidades excepcionales de comunicación que mejorarán tus relaciones profesionales, mejorarán la colaboración en equipo y impulsarán tus perspectivas de carrera.</p>

        <h3>Componentes Principales:</h3>
        <ul>
          <li><strong>Comunicación Verbal:</strong> Habilidades claras y confiadas de habla y presentación</li>
          <li><strong>Comunicación Escrita:</strong> Escritura profesional y documentación</li>
          <li><strong>Escucha Activa:</strong> Técnicas para mejor comprensión y compromiso</li>
          <li><strong>Comunicación No Verbal:</strong> Lenguaje corporal y comunicación visual</li>
          <li><strong>Resolución de Conflictos:</strong> Gestión efectiva de conversaciones difíciles</li>
        </ul>`,
      topics: language === 'en' ? [
        'Verbal and presentation skills',
        'Professional writing techniques',
        'Active listening strategies',
        'Non-verbal communication awareness',
        'Conflict resolution methods'
      ] : [
        'Habilidades verbales y de presentación',
        'Técnicas de escritura profesional',
        'Estrategias de escucha activa',
        'Conciencia de comunicación no verbal',
        'Métodos de resolución de conflictos'
      ],
      benefits: language === 'en' ? [
        'Better Professional Relationships',
        'Enhanced Team Collaboration',
        'Increased Confidence',
        'Career Advancement'
      ] : [
        'Mejores Relaciones Profesionales',
        'Colaboración en Equipo Mejorada',
        'Confianza Aumentada',
        'Avance Profesional'
      ],
      deliveryOptions: language === 'en' ? [
        'Interactive Workshops',
        'Role-playing Exercises',
        'Video Analysis',
        'Peer Feedback Sessions'
      ] : [
        'Talleres Interactivos',
        'Ejercicios de Juego de Roles',
        'Análisis de Video',
        'Sesiones de Retroalimentación de Pares'
      ],
      targetAudience: language === 'en' ? [
        'All professionals',
        'Team members',
        'Customer service staff',
        'Anyone seeking better communication'
      ] : [
        'Todos los profesionales',
        'Miembros del equipo',
        'Personal de servicio al cliente',
        'Cualquiera que busque mejor comunicación'
      ],
      icon: Users,
      price: language === 'en' ? 'Contact for pricing' : 'Contactar para precios',
      slug: 'communication-skills'
    },
    {
      id: 'oil-gas-training',
      title: language === 'en' ? 'Oil & Gas Training' : 'Entrenamiento de Petróleo y Gas',
      duration: language === 'en' ? '4-8 weeks' : '4-8 semanas',
      level: language === 'en' ? 'Intermediate to Advanced' : 'Intermedio a Avanzado',
      participants: '10-15',
      description: language === 'en' ? 'Professional training for energy sector standards' : 'Entrenamiento profesional para estándares del sector energético',
      fullDescription: language === 'en' ?
        `<h2>Oil & Gas Industry Training</h2>
        <p>Comprehensive training program designed specifically for professionals in the oil and gas industry, covering safety protocols, technical skills, and industry best practices.</p>

        <h3>Training Areas:</h3>
        <ul>
          <li><strong>Safety Protocols:</strong> HSE standards and emergency procedures</li>
          <li><strong>Technical Operations:</strong> Equipment operation and maintenance</li>
          <li><strong>Regulatory Compliance:</strong> Industry regulations and standards</li>
          <li><strong>Environmental Management:</strong> Sustainable practices and environmental protection</li>
          <li><strong>Quality Assurance:</strong> Quality control and assurance procedures</li>
        </ul>` :
        `<h2>Entrenamiento de la Industria de Petróleo y Gas</h2>
        <p>Programa de entrenamiento integral diseñado específicamente para profesionales en la industria de petróleo y gas, cubriendo protocolos de seguridad, habilidades técnicas y mejores prácticas de la industria.</p>

        <h3>Áreas de Entrenamiento:</h3>
        <ul>
          <li><strong>Protocolos de Seguridad:</strong> Estándares HSE y procedimientos de emergencia</li>
          <li><strong>Operaciones Técnicas:</strong> Operación y mantenimiento de equipos</li>
          <li><strong>Cumplimiento Regulatorio:</strong> Regulaciones y estándares de la industria</li>
          <li><strong>Gestión Ambiental:</strong> Prácticas sostenibles y protección ambiental</li>
          <li><strong>Aseguramiento de Calidad:</strong> Procedimientos de control y aseguramiento de calidad</li>
        </ul>`,
      topics: language === 'en' ? [
        'HSE standards and safety protocols',
        'Technical equipment operation',
        'Regulatory compliance requirements',
        'Environmental management practices',
        'Quality assurance procedures'
      ] : [
        'Estándares HSE y protocolos de seguridad',
        'Operación de equipos técnicos',
        'Requisitos de cumplimiento regulatorio',
        'Prácticas de gestión ambiental',
        'Procedimientos de aseguramiento de calidad'
      ],
      benefits: language === 'en' ? [
        'Industry Certification',
        'Enhanced Safety Knowledge',
        'Career Advancement',
        'Regulatory Compliance'
      ] : [
        'Certificación de la Industria',
        'Conocimiento de Seguridad Mejorado',
        'Avance Profesional',
        'Cumplimiento Regulatorio'
      ],
      deliveryOptions: language === 'en' ? [
        'On-site Training',
        'Simulation Exercises',
        'Certification Programs',
        'Continuing Education'
      ] : [
        'Entrenamiento en Sitio',
        'Ejercicios de Simulación',
        'Programas de Certificación',
        'Educación Continua'
      ],
      targetAudience: language === 'en' ? [
        'Oil & gas professionals',
        'Safety officers',
        'Technical operators',
        'Industry newcomers'
      ] : [
        'Profesionales de petróleo y gas',
        'Oficiales de seguridad',
        'Operadores técnicos',
        'Nuevos en la industria'
      ],
      icon: Award,
      price: language === 'en' ? 'Contact for pricing' : 'Contactar para precios',
      slug: 'oil-gas-training'
    }
        'New employees',
        'Teams needing upskilling'
      ],
      icon: Monitor,
      price: 'Contact for pricing',
      slug: 'corporate-pc-skills'
    },
    {
      id: 'leadership-training',
      title: 'Leadership Development Program',
      duration: '3-5 days',
      level: 'Management',
      participants: '10-15',
      description: 'Essential interpersonal, emotional, and strategic skills needed to lead effectively, inspire others, and drive organizational success.',
      fullDescription: `<h2>Leadership Development Program</h2>
      <p>Our Soft Skills Training: Leadership Program is designed to empower your team with the essential interpersonal, emotional, and strategic skills needed to lead effectively, inspire others, and drive organizational success.</p>
      
      <h3>Why Leadership Soft Skills Matter</h3>
      <p>Leadership is not just about making decisions or managing tasks; it's about inspiring trust, fostering collaboration, and creating a vision that motivates others to follow. Soft skills like communication, empathy, adaptability, and problem-solving are the building blocks of effective leadership.</p>
      
      <h3>Key Training Modules</h3>
      <ul>
        <li>Self-Awareness and Emotional Intelligence</li>
        <li>Effective Communication</li>
        <li>Building Trust and Influence</li>
        <li>Decision-Making and Problem-Solving</li>
        <li>Team Collaboration and Empowerment</li>
        <li>Adaptability and Change Management</li>
        <li>Visionary Leadership</li>
      </ul>`,
      topics: [
        'Self-Awareness and Emotional Intelligence',
        'Effective Communication and Active Listening',
        'Building Trust and Influence',
        'Decision-Making and Problem-Solving',
        'Team Collaboration and Empowerment',
        'Adaptability and Change Management',
        'Visionary Leadership'
      ],
      benefits: [
        'Stronger Leaders',
        'Improved Team Dynamics',
        'Higher Employee Engagement',
        'Better Decision-Making',
        'Organizational Growth'
      ],
      deliveryOptions: [
        'Workshops',
        'Virtual Training',
        'One-on-one Coaching',
        'Blended Learning'
      ],
      targetAudience: [
        'Emerging leaders',
        'Current managers',
        'Executives',
        'Teams at all levels'
      ],
      icon: Target,
      price: 'Contact for pricing',
      slug: 'leadership-training'
    },
    {
      id: 'time-management',
      title: 'Time Management Program',
      duration: '2-3 days',
      level: 'All Levels',
      participants: '15-25',
      description: 'Help individuals and teams take control of their schedules, prioritize effectively, and achieve more with less stress.',
      fullDescription: `<h2>Time Management Program</h2>
      <p>Our Time Management Program is designed to help individuals and teams take control of their schedules, prioritize effectively, and achieve more with less stress. In today's fast-paced world, mastering time management is essential for both personal and professional success.</p>

      <h3>Program Overview</h3>
      <p>This comprehensive program covers proven time management techniques, tools, and strategies that can be immediately applied to improve productivity and work-life balance.</p>

      <h3>Key Learning Areas</h3>
      <ul>
        <li>Understanding Time Management principles</li>
        <li>Goal Setting and Prioritization (SMART goals, Eisenhower Matrix)</li>
        <li>Planning and Scheduling techniques</li>
        <li>Overcoming Procrastination</li>
        <li>Managing Distractions and focus techniques</li>
        <li>Delegation and Collaboration</li>
        <li>Stress Management and Work-Life Balance</li>
      </ul>`,
      topics: [
        'Understanding Time Management principles',
        'Goal Setting and Prioritization (SMART goals, Eisenhower Matrix)',
        'Planning and Scheduling techniques',
        'Overcoming Procrastination',
        'Managing Distractions and focus techniques',
        'Delegation and Collaboration',
        'Stress Management and Work-Life Balance'
      ],
      benefits: [
        'Increased Productivity',
        'Better Work-Life Balance',
        'Reduced Stress',
        'Improved Focus',
        'Enhanced Goal Achievement'
      ],
      deliveryOptions: [
        'Interactive Workshops',
        'Virtual Training',
        'Self-paced Learning',
        'Group Coaching'
      ],
      targetAudience: [
        'Professionals at all levels',
        'Team leaders',
        'Entrepreneurs',
        'Anyone seeking better time management'
      ],
      icon: Clock,
      price: 'Contact for pricing',
      slug: 'time-management'
    },
    {
      id: 'communication-skills',
      title: 'Communication Skills Program',
      duration: '2-4 days',
      level: 'All Levels',
      participants: '12-20',
      description: 'Master the art of communication, fostering stronger connections, reducing misunderstandings, and driving better outcomes.',
      fullDescription: `<h2>Communication Skills Program</h2>
      <p>Our Communication Skills Program is designed to help participants master the art of communication, fostering stronger connections, reducing misunderstandings, and driving better outcomes in both personal and professional settings.</p>

      <h3>Why Communication Skills Matter</h3>
      <p>Effective communication is the foundation of successful relationships, teamwork, and leadership. Whether you're presenting to a board, collaborating with colleagues, or resolving conflicts, strong communication skills are essential.</p>

      <h3>Program Components</h3>
      <ul>
        <li>Foundations of Effective Communication</li>
        <li>Active Listening techniques</li>
        <li>Verbal and Non-Verbal Communication</li>
        <li>Written Communication skills</li>
        <li>Emotional Intelligence in Communication</li>
        <li>Conflict Resolution and Difficult Conversations</li>
        <li>Public Speaking and Presentation Skills</li>
      </ul>`,
      topics: [
        'Foundations of Effective Communication',
        'Active Listening techniques',
        'Verbal and Non-Verbal Communication',
        'Written Communication skills',
        'Emotional Intelligence in Communication',
        'Conflict Resolution and Difficult Conversations',
        'Public Speaking and Presentation Skills'
      ],
      benefits: [
        'Improved Relationships',
        'Better Team Collaboration',
        'Enhanced Leadership Skills',
        'Increased Confidence',
        'Reduced Conflicts'
      ],
      deliveryOptions: [
        'Interactive Workshops',
        'Role-playing Exercises',
        'Virtual Training',
        'One-on-one Coaching'
      ],
      targetAudience: [
        'Business professionals',
        'Team leaders',
        'Customer service representatives',
        'Anyone wanting to improve communication'
      ],
      icon: Users,
      price: 'Contact for pricing',
      slug: 'communication-skills'
    },
    {
      id: 'oil-gas-training',
      title: 'Professional Oil & Gas Training',
      duration: 'Varies',
      level: 'Professional',
      participants: '8-15',
      description: 'Certified, practical, and industry-relevant training to meet global energy sector standards with internationally recognized certifications.',
      fullDescription: `<h2>Professional Oil & Gas Training</h2>
      <p>Our Professional Oil & Gas Training programs are designed to provide certified, practical, and industry-relevant training that meets global energy sector standards. We offer internationally recognized certifications that enhance career prospects and ensure compliance with industry requirements.</p>

      <h3>Training Categories</h3>
      <ul>
        <li><strong>Offshore/Onshore Safety:</strong> OPITO, IWCF, IADC certified programs</li>
        <li><strong>Process Operations & Maintenance:</strong> Equipment operation and maintenance procedures</li>
        <li><strong>Pipeline & Refinery Operations:</strong> Specialized training for pipeline and refinery personnel</li>
        <li><strong>HSE (Health, Safety & Environment):</strong> Comprehensive safety and environmental training</li>
        <li><strong>Technical & Soft Skills Development:</strong> Both technical competencies and leadership skills</li>
      </ul>

      <h3>Certifications Offered</h3>
      <p>Our training programs lead to internationally recognized certifications from leading industry bodies including OPITO, IWCF, IADC, and other relevant organizations.</p>`,
      topics: [
        'Offshore/Onshore Safety (OPITO, IWCF, IADC)',
        'Process Operations & Maintenance',
        'Pipeline & Refinery Operations',
        'HSE (Health, Safety & Environment)',
        'Technical & Soft Skills Development'
      ],
      benefits: [
        'International Certifications',
        'Enhanced Career Prospects',
        'Industry Compliance',
        'Safety Excellence',
        'Technical Competency'
      ],
      deliveryOptions: [
        'On-site Training',
        'Simulation-based Learning',
        'Practical Workshops',
        'Certification Programs'
      ],
      targetAudience: [
        'Oil & Gas professionals',
        'Safety officers',
        'Operations personnel',
        'Maintenance technicians'
      ],
      icon: Award,
      price: 'Contact for pricing',
      slug: 'oil-gas-training'
    }
  ];

  // Use dynamic content if available, otherwise fallback to static data
  let program;
  if (trainingProgram) {
    // Use dynamic content from database
    program = {
      id: trainingProgram._id,
      title: trainingProgram.data.title,
      duration: trainingProgram.data.duration || (language === 'en' ? '2-4 weeks' : '2-4 semanas'),
      level: trainingProgram.data.level || (language === 'en' ? 'All Levels' : 'Todos los Niveles'),
      participants: trainingProgram.data.participants || '15-20',
      description: trainingProgram.data.description,
      fullDescription: trainingProgram.data.fullDescription || trainingProgram.data.description,
      topics: trainingProgram.data.topics || [],
      benefits: trainingProgram.data.benefits || [],
      deliveryOptions: trainingProgram.data.deliveryOptions || [],
      targetAudience: trainingProgram.data.targetAudience || [],
      icon: trainingProgram.data.icon || GraduationCap,
      price: trainingProgram.data.price || (language === 'en' ? 'Contact for pricing' : 'Contactar para precios'),
      slug: trainingProgram.data.slug
    };
  } else {
    // Fallback to static data
    program = fallbackPrograms.find(p => p.slug === slug);
  }

  if (!program) {
    return <Navigate to="/training" replace />;
  }

  const IconComponent = program.icon;

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Beginner': return 'bg-green-100 text-green-800';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'Advanced': return 'bg-red-100 text-red-800';
      case 'Management': return 'bg-purple-100 text-purple-800';
      case 'Professional': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const breadcrumbs = generateBreadcrumbs('training', program.title, language, t);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-white">
      {/* Breadcrumb */}
      <BreadcrumbNav items={breadcrumbs} />

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mr-4">
                  <IconComponent className="w-8 h-8 text-purple-600" />
                </div>
                <div>
                  <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
                    {program.title}
                  </h1>
                  <Badge className={`mt-2 ${getLevelColor(program.level)}`}>
                    {program.level}
                  </Badge>
                </div>
              </div>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                {program.description}
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/contact">
                  <Button 
                    size="lg"
                    className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4"
                  >
                    Enroll Now
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link to="/training">
                  <Button 
                    variant="outline"
                    size="lg"
                    className="px-8 py-4"
                  >
                    <ArrowLeft className="mr-2 h-5 w-5" />
                    Back to Programs
                  </Button>
                </Link>
              </div>
            </div>
            <div>
              <Card className="bg-white shadow-lg">
                <CardContent className="p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-6">{language === 'en' ? 'Program Details' : 'Detalles del Programa'}</h3>
                  <div className="grid grid-cols-2 gap-6">
                    <div className="text-center">
                      <Clock className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                      <div className="font-medium text-gray-900">{program.duration}</div>
                      <div className="text-gray-500 text-sm">{language === 'en' ? 'Duration' : 'Duración'}</div>
                    </div>
                    <div className="text-center">
                      <Users className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                      <div className="font-medium text-gray-900">{program.participants}</div>
                      <div className="text-gray-500 text-sm">{language === 'en' ? 'Participants' : 'Participantes'}</div>
                    </div>
                    <div className="text-center">
                      <GraduationCap className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                      <div className="font-medium text-gray-900">{language === 'en' ? 'Certificate' : 'Certificado'}</div>
                      <div className="text-gray-500 text-sm">{language === 'en' ? 'Included' : 'Incluido'}</div>
                    </div>
                    <div className="text-center">
                      <Target className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                      <div className="font-medium text-gray-900">{program.price}</div>
                      <div className="text-gray-500 text-sm">{language === 'en' ? 'Investment' : 'Inversión'}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Detailed Description */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div 
            className="prose prose-lg max-w-none text-gray-700 leading-relaxed"
            dangerouslySetInnerHTML={{ __html: program.fullDescription }}
          />
        </div>
      </section>

      {/* Program Content Grid */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Topics Covered */}
            <Card className="bg-white shadow-lg">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">{language === 'en' ? 'Topics Covered' : 'Temas Cubiertos'}</h3>
                <ul className="space-y-4">
                  {program.topics.map((topic, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="w-6 h-6 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{topic}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Benefits */}
            <Card className="bg-white shadow-lg">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">{language === 'en' ? 'Program Benefits' : 'Beneficios del Programa'}</h3>
                <ul className="space-y-4">
                  {program.benefits.map((benefit, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="w-6 h-6 text-purple-600 mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-purple-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            {language === 'en'
              ? 'Ready to Start Your Learning Journey?'
              : '¿Listo para Comenzar tu Viaje de Aprendizaje?'
            }
          </h2>
          <p className="text-xl text-purple-100 mb-8 max-w-2xl mx-auto">
            {language === 'en'
              ? 'Contact our training experts today to discuss how this program can benefit your team and organization.'
              : 'Contacta a nuestros expertos en entrenamiento hoy para discutir cómo este programa puede beneficiar a tu equipo y organización.'
            }
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/contact">
              <Button 
                size="lg"
                className="bg-white text-purple-600 hover:bg-gray-100 px-8 py-4"
              >
                {language === 'en' ? 'Enroll Now' : 'Inscribirse Ahora'}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/training">
              <Button 
                variant="outline"
                size="lg"
                className="border-white text-white hover:bg-white hover:text-purple-600 px-8 py-4"
              >
                {language === 'en' ? 'View All Programs' : 'Ver Todos los Programas'}
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

const TrainingDetail = () => {
  const { language } = useLanguage();

  return (
    <ContentProvider key={language} defaultLanguage={language}>
      <Layout>
        <TrainingDetailContent />
      </Layout>
    </ContentProvider>
  );
};

export default TrainingDetail;
